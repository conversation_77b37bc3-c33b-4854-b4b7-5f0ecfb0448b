# Tests d'Intégration Confluence

Ce répertoire contient les tests d'intégration pour le module Confluence avec une vraie instance Confluence.

## 🎯 Objectif

Les tests d'intégration permettent de valider le fonctionnement du loader Confluence avec une vraie instance Confluence, contrairement aux tests unitaires qui utilisent des mocks.

## 📁 Structure des Fichiers

```
tests/loader/confluence/
├── test_real_confluence_integration.py  # Tests d'intégration principaux
├── run_integration_tests.py            # Script utilitaire pour exécuter les tests
├── .env.example                        # Exemple de configuration
├── README_INTEGRATION.md               # Ce fichier
└── ...                                 # Autres tests
```

## 🚀 Configuration Rapide

### 1. Prérequis

- Accès à une instance Confluence (Atlassian Cloud ou Server)
- Personal Access Token (PAT) ou credentials API
- Un espace de test avec quelques pages

### 2. Configuration

1. **Copiez le fichier de configuration :**
   ```bash
   cp tests/loader/confluence/.env.example tests/loader/confluence/.env
   ```

2. **Éditez le fichier .env :**
   ```bash
   # Configuration obligatoire
   CONFLUENCE_URL=https://votre-instance.atlassian.net
   CONFLUENCE_PAT_TOKEN=votre_personal_access_token
   
   # Configuration optionnelle
   CONFLUENCE_TEST_SPACE=TEST
   CONFLUENCE_TIMEOUT=30
   ```

3. **Vérifiez la configuration :**
   ```bash
   cd tests/loader/confluence
   python run_integration_tests.py --check-config
   ```

## 🧪 Exécution des Tests

### Tests Rapides (Recommandé pour débuter)

```bash
# Vérification de la connexion uniquement
python run_integration_tests.py --health-check

# Tests rapides (sans téléchargement de documents)
python run_integration_tests.py --run-fast
```

### Tests Complets

```bash
# Tous les tests d'intégration
python run_integration_tests.py --run-all

# Test spécifique
python run_integration_tests.py --run-specific test_confluence_connection
```

### Avec pytest directement

```bash
# Tous les tests d'intégration
pytest tests/loader/confluence/test_real_confluence_integration.py -v -m integration

# Tests rapides uniquement
pytest tests/loader/confluence/test_real_confluence_integration.py -v -m "integration and not slow"

# Test spécifique
pytest tests/loader/confluence/test_real_confluence_integration.py::TestRealConfluenceIntegration::test_health_check_real -v
```

## 📋 Types de Tests Disponibles

### 1. `test_confluence_connection`
- **Objectif** : Vérifier que le loader Confluence peut être instancié
- **Durée** : Très rapide (< 1s)
- **Prérequis** : Configuration de base

### 2. `test_health_check_real`
- **Objectif** : Vérifier la connexion à l'instance Confluence
- **Durée** : Rapide (< 5s)
- **Prérequis** : Credentials valides

### 3. `test_get_document_list_real`
- **Objectif** : Récupérer la liste des documents depuis Confluence
- **Durée** : Moyen (5-15s)
- **Prérequis** : Espace de test avec des pages

### 4. `test_get_document_real` (marqué `@pytest.mark.slow`)
- **Objectif** : Télécharger un document complet
- **Durée** : Lent (15-60s)
- **Prérequis** : Espace de test avec des pages

## 🔧 Configuration Avancée

### Variables d'Environnement

| Variable | Obligatoire | Description | Exemple |
|----------|-------------|-------------|---------|
| `CONFLUENCE_URL` | ✅ | URL de l'instance Confluence | `https://company.atlassian.net` |
| `CONFLUENCE_PAT_TOKEN` | ✅ | Personal Access Token | `ATATTxxx...` |
| `CONFLUENCE_TEST_SPACE` | ❌ | Espace de test | `TEST` (défaut) |
| `CONFLUENCE_TIMEOUT` | ❌ | Timeout en secondes | `30` (défaut) |

### Création d'un Personal Access Token

1. Allez dans **Confluence** → **Settings** → **Personal Access Tokens**
2. Cliquez sur **Create token**
3. Donnez un nom au token (ex: "Tests d'intégration")
4. Sélectionnez les permissions : **Read** sur les espaces de test
5. Copiez le token généré

### Préparation de l'Espace de Test

Pour des tests optimaux, créez un espace de test avec :
- 3-5 pages avec du contenu varié
- 1-2 pages avec des pièces jointes (PDF, images)
- Une hiérarchie de pages (page parent → sous-pages)

## 🐛 Dépannage

### Erreur "Configuration Confluence non disponible"
- Vérifiez que le fichier `.env` existe et contient les bonnes variables
- Vérifiez que `CONFLUENCE_URL` et `CONFLUENCE_PAT_TOKEN` sont définis

### Erreur "Health check échoué"
- Vérifiez que l'URL Confluence est accessible
- Vérifiez que le PAT token est valide et non expiré
- Vérifiez les permissions du token

### Erreur "Aucun document trouvé"
- Vérifiez que l'espace de test existe
- Vérifiez que l'espace contient des pages
- Vérifiez les permissions de lecture sur l'espace

### Tests lents
- Utilisez `--run-fast` pour éviter les tests marqués `@pytest.mark.slow`
- Réduisez `max_results` dans la configuration de test

## 📊 Interprétation des Résultats

### Succès ✅
```
✅ Health check réussi - Instance Confluence accessible
   URL: https://company.atlassian.net
   Espace de test: TEST
   Documents trouvés: 5
```

### Échec ❌
```
❌ Health check échoué: 401 Unauthorized
```

## 🔄 Intégration CI/CD

Pour intégrer ces tests dans votre pipeline CI/CD :

```yaml
# Exemple GitHub Actions
- name: Tests d'intégration Confluence
  env:
    CONFLUENCE_URL: ${{ secrets.CONFLUENCE_URL }}
    CONFLUENCE_PAT_TOKEN: ${{ secrets.CONFLUENCE_PAT_TOKEN }}
    CONFLUENCE_TEST_SPACE: "CI_TEST"
  run: |
    python tests/loader/confluence/run_integration_tests.py --run-fast
```

## 📝 Bonnes Pratiques

1. **Utilisez un espace de test dédié** - Ne testez jamais sur des données de production
2. **Limitez les résultats** - Utilisez `max_results: 5-10` pour les tests
3. **Gérez les secrets** - Ne commitez jamais les vraies credentials
4. **Tests progressifs** - Commencez par `--health-check`, puis `--run-fast`
5. **Nettoyage** - Les tests utilisent des répertoires temporaires qui se nettoient automatiquement
